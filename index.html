<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Netvis Solution</title>
  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              50: '#f0f9ff',
              100: '#e0f2fe',
              200: '#bae6fd',
              300: '#7dd3fc',
              400: '#38bdf8',
              500: '#0ea5e9',
              600: '#0284c7',
              700: '#0369a1',
              800: '#075985',
              900: '#0c4a6e',
              950: '#082f49',
            },
          },
          animation: {
            'float': 'float 3s ease-in-out infinite',
            'ping': 'ping 1s cubic-bezier(0, 0, 0.2, 1) infinite',
            'fade-in': 'fadeIn 0.7s ease-in-out',
            'slide-up': 'slideUp 0.7s ease-out',
            'zoom-in': 'zoomIn 0.7s ease-out',
          },
          keyframes: {
            float: {
              '0%, 100%': {
                transform: 'translateY(0)'
              },
              '50%': {
                transform: 'translateY(-10px)'
              },
            },
            ping: {
              '75%, 100%': {
                transform: 'scale(2)',
                opacity: '0'
              },
            },
            fadeIn: {
              '0%': { opacity: '0', transform: 'translateY(20px)' },
              '100%': { opacity: '1', transform: 'translateY(0)' },
            },
            slideUp: {
              '0%': { opacity: '0', transform: 'translateY(30px)' },
              '100%': { opacity: '1', transform: 'translateY(0)' },
            },
            zoomIn: {
              '0%': { opacity: '0', transform: 'scale(0.9)' },
              '100%': { opacity: '1', transform: 'scale(1)' },
            }
          },
        },
      },
    }
  </script>
  <style>
    /* Background styling */
    body {
      background-image:
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.05) 0%, transparent 50%),
        linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
      background-attachment: fixed;
      background-size: 100% 100%, 100% 100%, 100% 100%;
      min-height: 100vh;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes fadeOut {
      from {
        opacity: 1;
        transform: translateY(0);
      }

      to {
        opacity: 0;
        transform: translateY(-20px);
      }
    }

    .animate-fade-in {
      animation: fadeIn 0.5s ease-out forwards;
    }

    .animate-fade-out {
      animation: fadeOut 0.5s ease-out forwards;
    }

    .tab-content {
      display: none !important;
      opacity: 0;
      visibility: hidden;
      position: absolute;
      left: -9999px;
      transform: translateY(10px);
      transition: opacity 0.3s ease, transform 0.3s ease;
    }

    .tab-content.active {
      display: block !important;
      opacity: 1;
      visibility: visible;
      position: static;
      transform: translateY(0);
      animation: fadeIn 0.5s ease-out forwards;
    }

    .tab-trigger {
      position: relative;
      transition: all 0.3s ease;
    }

    .tab-trigger::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      width: 0;
      height: 2px;
      background: linear-gradient(to right, #0EA5E9, #3B82F6);
      transition: width 0.3s ease;
    }

    .tab-trigger.bg-white::after {
      width: 100%;
    }

    .accordion-content {
      max-height: 0;
      overflow: hidden;
      opacity: 0;
      transform: translateY(-10px);
      transition: max-height 0.5s ease, opacity 0.3s ease, transform 0.3s ease, background-color 0.3s ease;
      display: none;
    }

    .accordion-content.active {
      max-height: 500px;
      opacity: 1;
      transform: translateY(0);
      display: block;
    }

    /* FAQ specific accordion styling */
    #faq .accordion-content {
      border-top: 1px solid rgba(229, 231, 235, 0.5);
      margin-top: 0;
      padding-top: 1rem;
      overflow: hidden;
    }

    #faq .accordion-trigger svg {
      transition: none;
    }

    #faq .accordion-trigger.active svg {
      transform: rotate(180deg);
    }

    /* Mobile menu specific animations */
    #mobile-menu {
      box-shadow: 0 0 25px rgba(0, 0, 0, 0.15);
      transform: translateX(-100%) scale(0.95);
      transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.3s ease;
      will-change: transform, opacity;
    }

    /* How It Works tab styling */
    .tab-trigger {
      position: relative;
      overflow: hidden;
    }

    .tab-trigger::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.1);
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      z-index: 0;
    }

    .tab-trigger:hover::before {
      transform: translateX(0);
    }

    .tab-content {
      display: none;
      opacity: 0;
      transform: translateY(10px);
      transition: opacity 0.3s ease, transform 0.3s ease;
    }

    .tab-content.active {
      display: block;
      opacity: 1;
      transform: translateY(0);
    }

    #mobile-menu.translate-x-0 {
      transform: translateX(0) scale(1);
    }

    #mobile-menu-overlay {
      transition: opacity 0.4s ease;
      opacity: 1;
    }

    #mobile-menu-overlay.hidden {
      opacity: 0;
    }

    /* Custom scrollbar (optional, for aesthetics) */
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb {
      background: #0ea5e9;
      /* sky-500 */
      border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: #0284c7;
      /* sky-600 */
    }

    .accordion-content {
      transition: max-height 0.35s ease-in-out, padding-top 0.35s ease-in-out, padding-bottom 0.35s ease-in-out;
      overflow: hidden;
      /* Ensure overflow is hidden for max-height transition */
    }

    /* Style for the active accordion trigger (optional, if you want to change the button appearance) */
    /* .accordion-trigger.active {
      background-color: #f0f0f0;
    } */

    .tab-content {
      display: none;
      /* Hide tab content by default */
    }

    .tab-content.active {
      display: block;
      /* Show active tab content */
    }

    /* Enhanced slider styles */
    /* Extra small screen support */
    @media (max-width: 475px) {
      .xs\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem;
      }

      .xs\:text-base {
        font-size: 1rem;
        line-height: 1.5rem;
      }

      .xs\:max-w-\[320px\] {
        max-width: 320px;
      }
    }

    /* Mobile-specific improvements */
    @media (max-width: 640px) {

      /* Improve touch targets */
      button {
        min-height: 44px;
        min-width: 44px;
      }

      /* Better text readability on mobile */
      #hero-section h1 {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      #hero-section p {
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      }

      /* Optimize image loading on mobile */
      #hero-section img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
      }
    }

    /* Ensure animations can be re-triggered */
    .animate-on-change {
      animation: none;
    }

    .animate-on-change.is-animating {
      animation: slideUp 0.7s ease-out;
    }

    .animate-image-on-change {
      animation: none;
    }

    .animate-image-on-change.is-animating {
      animation: zoomIn 0.7s ease-out;
    }

    /* Smooth scrolling for better mobile experience */
    html {
      scroll-behavior: smooth;
    }

    /* Prevent horizontal scroll on mobile */
    body {
      overflow-x: hidden;
    }
  </style>
</head>

<body class="min-h-screen bg-gradient-to-b from-gray-50 to-white">
  <!-- Header -->
  <header class="sticky top-0 z-50 w-full border-b border-gray-100 bg-white/80 backdrop-blur-xl">
    <div class="container mx-auto flex h-16 items-center justify-between px-4 md:px-6">
      <a href="/" class="flex items-center">
        <img src="public/logo.webp" alt="Logo" class="h-10 w-auto">
      </a>

      <!-- Desktop Navigation -->
      <div class="hidden md:flex md:gap-10">
        <nav>
          <ul class="flex space-x-4">
            <li>
              <a href="#"
                class="inline-flex h-9 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors hover:bg-gray-100 hover:text-gray-900 focus:bg-gray-100 focus:text-gray-900 focus:outline-none bg-gray-100/50">Home</a>
            </li>
            <li class="relative group">
              <a href="#"
                class="inline-flex h-9 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors hover:bg-gray-100 hover:text-gray-900 focus:bg-gray-100 focus:text-gray-900 focus:outline-none">About
                Us</a>
              <div
                class="absolute left-0 top-full z-50 hidden min-w-[200px] rounded-md bg-white p-2 shadow-lg group-hover:block">
                <a href="#" class="block rounded-md p-2 text-sm hover:bg-gray-100">About Us</a>
                <a href="#" class="block rounded-md p-2 text-sm hover:bg-gray-100">Our Vision</a>
                <a href="#" class="block rounded-md p-2 text-sm hover:bg-gray-100">Our Mission</a>
              </div>
            </li>
            <li>
              <a href="#"
                class="inline-flex h-9 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors hover:bg-gray-100 hover:text-gray-900 focus:bg-gray-100 focus:text-gray-900 focus:outline-none">Our
                Blog</a>
            </li>
            <li>
              <a href="#"
                class="inline-flex h-9 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors hover:bg-gray-100 hover:text-gray-900 focus:bg-gray-100 focus:text-gray-900 focus:outline-none">Our
                Products & Services</a>
            </li>
            <li>
              <a href="#"
                class="inline-flex h-9 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors hover:bg-gray-100 hover:text-gray-900 focus:bg-gray-100 focus:text-gray-900 focus:outline-none">Contact</a>
            </li>
          </ul>
        </nav>
      </div>

      <!-- Auth Buttons -->
      <div class="hidden md:flex md:items-center md:gap-4">
        <button class="h-9 px-4 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md font-medium">Sign
          In</button>
        <button
          class="h-9 px-4 bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white rounded-md shadow-md shadow-blue-500/20 hover:shadow-lg hover:shadow-blue-500/30 transition-all duration-300">Sign
          Up</button>
      </div>

      <!-- Mobile Menu Button -->
      <button id="mobile-menu-button" class="md:hidden rounded-md p-2 text-gray-700 hover:bg-gray-100">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
          <line x1="3" y1="12" x2="21" y2="12"></line>
          <line x1="3" y1="6" x2="21" y2="6"></line>
          <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
        <span class="sr-only">Toggle menu</span>
      </button>
    </div>
  </header>

  <!-- Mobile Menu -->
  <div id="mobile-menu-overlay" class="fixed inset-0 bg-black/50 z-40 hidden"></div>
  <div id="mobile-menu"
    class="fixed top-0 left-0 w-[85%] max-w-[300px] h-full bg-white z-50 transform -translate-x-full transition-all duration-500 ease-in-out overflow-y-auto shadow-2xl">
    <div class="bg-white p-6 border-b">
      <div class="flex justify-between items-center">
        <a href="/" class="flex items-center">
          <img src="public/logo.webp" alt="Logo" class="h-8 w-auto">
        </a>
        <button id="close-mobile-menu" class="rounded-md p-2 text-gray-700 hover:bg-gray-100">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
          <span class="sr-only">Close menu</span>
        </button>
      </div>

    </div>
    <nav class="flex flex-col gap-4 p-6">
      <a href="#" class="text-lg font-medium hover:text-sky-600 transition-colors py-2">Home</a>

      <div class="border-b pb-2">
        <button
          class="accordion-trigger flex w-full justify-between items-center text-lg font-medium hover:text-sky-600 transition-colors py-2"
          data-target="about-content">
          About Us
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="h-5 w-5 transition-transform">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </button>
        <div id="about-content" class="accordion-content pl-4 pt-2">
          <div class="flex flex-col space-y-2">
            <a href="#" class="text-gray-700 hover:text-sky-600 transition-colors py-2">About Us</a>
            <a href="#" class="text-gray-700 hover:text-sky-600 transition-colors py-2">Our Vision</a>
            <a href="#" class="text-gray-700 hover:text-sky-600 transition-colors py-2">Our Mission</a>
          </div>
        </div>
      </div>

      <a href="#" class="text-lg font-medium hover:text-sky-600 transition-colors py-2">Our Blog</a>
      <a href="#" class="text-lg font-medium hover:text-sky-600 transition-colors py-2">Our Products & Services</a>
      <a href="#" class="text-lg font-medium hover:text-sky-600 transition-colors py-2">Contact</a>

      <div class="flex flex-col gap-3 mt-4 pt-4 border-t">
        <button
          class="py-3 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">Sign
          In</button>
        <button
          class="py-3 px-4 bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white rounded-md shadow-md shadow-blue-500/20 hover:shadow-lg hover:shadow-blue-500/30 transition-all duration-300">Sign
          Up</button>
      </div>
    </nav>
  </div>

  <main>
    <!-- Hero Section -->
    <section id="hero-section" class="relative w-full overflow-hidden">
      <!-- Background Image Container -->
      <div id="hero-bg-container" class="absolute inset-0 w-full h-full">
        <!-- Initial Background Image (will be cloned for transitions) -->
        <img src="https://images.unsplash.com/photo-1521587760476-6c12a4b040da?q=80&w=2070&auto=format&fit=crop"
          alt="Background" class="w-full h-full object-cover transition-opacity duration-1000 ease-in-out">
      </div>

      <!-- Gradient Overlay for text readability -->
      <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/50 to-black/20"></div>
      <div class="absolute inset-0 bg-gradient-to-r from-black/30 to-transparent"></div>

      <!-- Main Slider Content -->
      <div
        class="relative min-h-[85vh] sm:min-h-[90vh] lg:min-h-screen flex flex-col justify-center py-8 sm:py-12 lg:py-0">
        <div class="container mx-auto px-3 sm:px-4 lg:px-8 relative z-10">
          <div class="flex flex-col lg:grid lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-16 items-center">

            <!-- Image Content: First on mobile for better visual hierarchy -->
            <div class="flex justify-center lg:justify-end order-1 lg:order-2 w-full">
              <div id="hero-image-container"
                class="animate-image-on-change w-full max-w-[280px] xs:max-w-[320px] sm:max-w-sm md:max-w-md lg:max-w-lg">
                <div class="relative" style="padding-bottom: 120%;"> <!-- Slightly reduced aspect ratio for mobile -->
                  <img id="hero-poster"
                    src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                    alt="E Books Bundle"
                    class="absolute inset-0 w-full h-full object-cover rounded-xl sm:rounded-2xl shadow-2xl border-2 sm:border-4 border-white/20 transition-all duration-500 ease-in-out lg:transform lg:hover:rotate-3">
                </div>
              </div>
            </div>

            <!-- Text Content: Second on mobile with improved spacing -->
            <div id="hero-text"
              class="text-white space-y-3 sm:space-y-4 lg:space-y-6 text-center lg:text-left order-2 lg:order-1 w-full px-2 sm:px-0">
              <div class="animate-on-change">
                <span
                  class="inline-block bg-white/10 backdrop-blur-sm text-xs sm:text-sm font-medium px-3 sm:px-4 py-1 sm:py-1.5 rounded-full mb-2 sm:mb-3">E-Books</span>
                <h1
                  class="text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold tracking-tight leading-tight">
                  E Books Bundle
                </h1>
                <p
                  class="text-sm xs:text-base sm:text-lg lg:text-xl text-white/85 max-w-md lg:max-w-lg mx-auto lg:mx-0 mt-3 sm:mt-4 leading-relaxed">
                  Discover a world of knowledge with our extensive E-Book collection. Save up to 75% on premium titles.
                </p>
                <button
                  class="mt-4 sm:mt-6 lg:mt-8 bg-white text-gray-900 hover:bg-gray-200 px-6 sm:px-8 py-2.5 sm:py-3 rounded-lg sm:rounded-full shadow-lg font-semibold text-sm sm:text-base transition-all duration-300 transform hover:scale-105">
                  Explore Now
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div
        class="absolute bottom-4 sm:bottom-6 left-1/2 -translate-x-1/2 z-20 w-full px-3 sm:px-4 flex items-center justify-center">
        <div
          class="flex items-center space-x-3 sm:space-x-4 bg-black/30 backdrop-blur-md p-1.5 sm:p-2 rounded-full border border-white/10">
          <button id="prev-slide" aria-label="Previous slide"
            class="p-1.5 sm:p-2 text-white hover:bg-white/20 rounded-full transition-colors touch-manipulation">
            <svg class="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <div id="hero-indicators" class="flex items-center space-x-1.5 sm:space-x-2 px-1 sm:px-2">
            <!-- Indicators will be dynamically generated -->
          </div>

          <button id="next-slide" aria-label="Next slide"
            class="p-1.5 sm:p-2 text-white hover:bg-white/20 rounded-full transition-colors touch-manipulation">
            <svg class="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 relative overflow-hidden">
      <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-white to-gray-50 -z-10"></div>
      <div
        class="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-b from-sky-100 to-transparent rounded-full blur-3xl opacity-60 -z-10 transform translate-x-1/2 -translate-y-1/2">
      </div>
      <div
        class="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-t from-blue-100 to-transparent rounded-full blur-3xl opacity-60 -z-10 transform -translate-x-1/2 translate-y-1/2">
      </div>

      <div class="container mx-auto px-4 md:px-6">
        <div class="text-center space-y-4 mb-16">
          <div
            class="inline-flex items-center justify-center px-4 py-1.5 rounded-full bg-gradient-to-r from-sky-500/10 to-blue-500/10 text-sm font-medium text-sky-700 mb-4">
            <span class="relative flex h-2 w-2 mr-2">
              <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-sky-400 opacity-75"></span>
              <span class="relative inline-flex rounded-full h-2 w-2 bg-sky-500"></span>
            </span>
            One-Stop Solution
          </div>
          <h2
            class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900">
            Everything You Need, All in One Place
          </h2>
          <p class="mx-auto max-w-[700px] text-gray-500 md:text-xl/relaxed">
            Discover our wide range of products and services designed to make your life easier and more rewarding.
          </p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 px-4 sm:px-0">
          <!-- Feature Card 1 -->
          <a href="#" class="group">
            <div
              class="bg-white border border-gray-100 shadow-md hover:shadow-xl transition-all duration-500 rounded-xl overflow-hidden h-full transform hover:-translate-y-1">
              <div
                class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-amber-500 to-orange-600 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-500">
              </div>
              <div class="p-4 sm:p-6">
                <div
                  class="flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gradient-to-r from-amber-500/20 to-orange-500/20 group-hover:from-amber-500/30 group-hover:to-orange-500/30 mb-3 sm:mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="text-amber-600 h-5 w-5 sm:h-6 sm:w-6">
                    <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <path d="M16 10a4 4 0 0 1-8 0"></path>
                  </svg>
                </div>
                <h3 class="text-lg sm:text-xl font-bold mb-2 transition-colors duration-300 group-hover:text-amber-600">
                  Online Shopping</h3>
                <p class="text-gray-500 mb-4 text-sm sm:text-base">Shop from thousands of products with amazing
                  discounts and cashback offers.</p>
                <div class="flex items-center text-gray-500 group-hover:text-gray-700 transition-colors duration-300">
                  <span class="mr-2 text-sm sm:text-base">Learn more</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1">
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                    <polyline points="12 5 19 12 12 19"></polyline>
                  </svg>
                </div>
              </div>
            </div>
          </a>

          <!-- Feature Card 2 -->
          <a href="#" class="group">
            <div
              class="bg-white border border-gray-100 shadow-md hover:shadow-xl transition-all duration-500 rounded-xl overflow-hidden h-full transform hover:-translate-y-1">
              <div
                class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-500 to-amber-600 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-500">
              </div>
              <div class="p-4 sm:p-6">
                <div
                  class="flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gradient-to-r from-yellow-500/20 to-amber-500/20 group-hover:from-yellow-500/30 group-hover:to-amber-500/30 mb-3 sm:mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="text-yellow-600 h-5 w-5 sm:h-6 sm:w-6">
                    <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                    <line x1="1" y1="10" x2="23" y2="10"></line>
                  </svg>
                </div>
                <h3
                  class="text-lg sm:text-xl font-bold mb-2 transition-colors duration-300 group-hover:text-yellow-600">
                  Bill Payments</h3>
                <p class="text-gray-500 mb-4 text-sm sm:text-base">Pay all your bills in one place and earn cashback on
                  every transaction.</p>
                <div class="flex items-center text-gray-500 group-hover:text-gray-700 transition-colors duration-300">
                  <span class="mr-2 text-sm sm:text-base">Learn more</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1">
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                    <polyline points="12 5 19 12 12 19"></polyline>
                  </svg>
                </div>
              </div>
            </div>
          </a>

          <!-- Feature Card 3 -->
          <a href="#" class="group">
            <div
              class="bg-white border border-gray-100 shadow-md hover:shadow-xl transition-all duration-500 rounded-xl overflow-hidden h-full transform hover:-translate-y-1">
              <div
                class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-sky-500 to-cyan-600 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-500">
              </div>
              <div class="p-4 sm:p-6">
                <div
                  class="flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gradient-to-r from-sky-500/20 to-cyan-500/20 group-hover:from-sky-500/30 group-hover:to-cyan-500/30 mb-3 sm:mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="text-sky-600 h-5 w-5 sm:h-6 sm:w-6">
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                    </path>
                    <path d="M14.05 2a9 9 0 0 1 8 7.94"></path>
                    <path d="M14.05 6A5 5 0 0 1 18 10"></path>
                  </svg>
                </div>
                <h3 class="text-lg sm:text-xl font-bold mb-2 transition-colors duration-300 group-hover:text-sky-600">
                  Travel Booking</h3>
                <p class="text-gray-500 mb-4 text-sm sm:text-base">Book flights, hotels, buses, and more with exclusive
                  discounts.</p>
                <div class="flex items-center text-gray-500 group-hover:text-gray-700 transition-colors duration-300">
                  <span class="mr-2 text-sm sm:text-base">Learn more</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1">
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                    <polyline points="12 5 19 12 12 19"></polyline>
                  </svg>
                </div>
              </div>
            </div>
          </a>

          <!-- Feature Card 4 -->
          <a href="#" class="group">
            <div
              class="bg-white border border-gray-100 shadow-md hover:shadow-xl transition-all duration-500 rounded-xl overflow-hidden h-full transform hover:-translate-y-1">
              <div
                class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-emerald-500 to-teal-600 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-500">
              </div>
              <div class="p-4 sm:p-6">
                <div
                  class="flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gradient-to-r from-emerald-500/20 to-teal-500/20 group-hover:from-emerald-500/30 group-hover:to-teal-500/30 mb-3 sm:mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="text-emerald-600 h-5 w-5 sm:h-6 sm:w-6">
                    <rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect>
                    <line x1="12" y1="18" x2="12.01" y2="18"></line>
                  </svg>
                </div>
                <h3
                  class="text-lg sm:text-xl font-bold mb-2 transition-colors duration-300 group-hover:text-emerald-600">
                  Mobile Recharge</h3>
                <p class="text-gray-500 mb-4 text-sm sm:text-base">Quick and easy recharge options for all your mobile
                  and data needs.</p>
                <div class="flex items-center text-gray-500 group-hover:text-gray-700 transition-colors duration-300">
                  <span class="mr-2 text-sm sm:text-base">Learn more</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1">
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                    <polyline points="12 5 19 12 12 19"></polyline>
                  </svg>
                </div>
              </div>
            </div>
          </a>

          <!-- Feature Card 5 -->
          <a href="#" class="group">
            <div
              class="bg-white border border-gray-100 shadow-md hover:shadow-xl transition-all duration-500 rounded-xl overflow-hidden h-full transform hover:-translate-y-1">
              <div
                class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-rose-500 to-red-600 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-500">
              </div>
              <div class="p-4 sm:p-6">
                <div
                  class="flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gradient-to-r from-rose-500/20 to-red-500/20 group-hover:from-rose-500/30 group-hover:to-red-500/30 mb-3 sm:mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="text-rose-600 h-5 w-5 sm:h-6 sm:w-6">
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                  </svg>
                </div>
                <h3 class="text-lg sm:text-xl font-bold mb-2 transition-colors duration-300 group-hover:text-rose-600">
                  Insurance</h3>
                <p class="text-gray-500 mb-4 text-sm sm:text-base">Secure your future with our comprehensive insurance
                  solutions.</p>
                <div class="flex items-center text-gray-500 group-hover:text-gray-700 transition-colors duration-300">
                  <span class="mr-2 text-sm sm:text-base">Learn more</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1">
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                    <polyline points="12 5 19 12 12 19"></polyline>
                  </svg>
                </div>
              </div>
            </div>
          </a>

          <!-- Feature Card 6 -->
          <a href="#" class="group">
            <div
              class="bg-white border border-gray-100 shadow-md hover:shadow-xl transition-all duration-500 rounded-xl overflow-hidden h-full transform hover:-translate-y-1">
              <div
                class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-600 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-500">
              </div>
              <div class="p-4 sm:p-6">
                <div
                  class="flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gradient-to-r from-blue-500/20 to-indigo-500/20 group-hover:from-blue-500/30 group-hover:to-indigo-500/30 mb-3 sm:mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="text-blue-600 h-5 w-5 sm:h-6 sm:w-6">
                    <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
                    <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
                  </svg>
                </div>
                <h3 class="text-lg sm:text-xl font-bold mb-2 transition-colors duration-300 group-hover:text-blue-600">
                  E-Books Bundle</h3>
                <p class="text-gray-500 mb-4 text-sm sm:text-base">Discover a world of knowledge with our extensive
                  E-Book collection.</p>
                <div class="flex items-center text-gray-500 group-hover:text-gray-700 transition-colors duration-300">
                  <span class="mr-2 text-sm sm:text-base">Learn more</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1">
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                    <polyline points="12 5 19 12 12 19"></polyline>
                  </svg>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>
    </section>

    <!-- Download Mobile App Section -->
    <section class="py-20 bg-gradient-to-r from-gray-50 to-gray-100 relative overflow-hidden">
      <div
        class="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-b from-sky-100 to-transparent rounded-full blur-3xl opacity-60 -z-10 transform translate-x-1/2 -translate-y-1/2">
      </div>
      <div
        class="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-t from-blue-100 to-transparent rounded-full blur-3xl opacity-60 -z-10 transform -translate-x-1/2 translate-y-1/2">
      </div>

      <div class="container mx-auto px-4 md:px-6">
        <div class="grid md:grid-cols-2 gap-12 items-center">
          <div class="order-2 md:order-1">
            <div class="relative">
              <div
                class="absolute inset-0 bg-gradient-to-r from-sky-500/20 to-blue-500/20 rounded-3xl blur-3xl opacity-30 -z-10 transform -rotate-6">
              </div>
              <div class="relative bg-white rounded-3xl p-2 shadow-2xl shadow-blue-500/10 overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-sky-50 to-blue-50 opacity-50">
                </div>
                <img
                  src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                  alt="Mobile App" class="w-full h-full object-cover rounded-2xl relative z-10" />
                <div class="absolute inset-0 bg-gradient-to-t from-blue-600/40 to-transparent rounded-2xl z-20"></div>
              </div>
              <div
                class="absolute -bottom-6 -right-6 w-24 h-24 bg-gradient-to-br from-sky-500 to-blue-600 rounded-full blur-2xl opacity-20">
              </div>
              <div
                class="absolute -top-6 -left-6 w-24 h-24 bg-gradient-to-br from-sky-300 to-blue-400 rounded-full blur-2xl opacity-20">
              </div>
            </div>
          </div>

          <div class="order-1 md:order-2">
            <div class="space-y-4">
              <div
                class="inline-flex items-center justify-center px-4 py-1.5 rounded-full bg-gradient-to-r from-sky-500/10 to-blue-500/10 text-sm font-medium text-sky-700 mb-4">
                <span class="relative flex h-2 w-2 mr-2">
                  <span
                    class="animate-ping absolute inline-flex h-full w-full rounded-full bg-sky-400 opacity-75"></span>
                  <span class="relative inline-flex rounded-full h-2 w-2 bg-sky-500"></span>
                </span>
                Mobile Experience
              </div>
              <h2
                class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900">
                Download Our Mobile App
              </h2>
              <p class="text-gray-600 md:text-xl/relaxed">
                Get the best experience with our mobile app. Access all our services on the go and enjoy exclusive
                mobile-only offers.
              </p>

              <div class="space-y-6 mt-8">
                <div class="flex items-start gap-4">
                  <div
                    class="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-gradient-to-r from-sky-500/20 to-blue-500/20 text-sky-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="h-6 w-6">
                      <path d="M12 19H5a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v5.5"></path>
                      <path d="M16 3v4"></path>
                      <path d="M8 3v4"></path>
                      <path d="M20.2 15.2L15 20.4"></path>
                      <path d="M15 15.2l5.2 5.2"></path>
                    </svg>
                  </div>
                  <div class="space-y-1">
                    <h3 class="text-xl font-semibold">Easy to Use</h3>
                    <p class="text-gray-500">
                      Intuitive interface designed for seamless navigation and quick access to all features.
                    </p>
                  </div>
                </div>

                <div class="flex items-start gap-4">
                  <div
                    class="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-gradient-to-r from-sky-500/20 to-blue-500/20 text-sky-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="h-6 w-6">
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                  </div>
                  <div class="space-y-1">
                    <h3 class="text-xl font-semibold">Exclusive Offers</h3>
                    <p class="text-gray-500">
                      Get access to app-only deals, discounts, and promotions that aren't available elsewhere.
                    </p>
                  </div>
                </div>

                <div class="flex items-start gap-4">
                  <div
                    class="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-gradient-to-r from-sky-500/20 to-blue-500/20 text-sky-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="h-6 w-6">
                      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                    </svg>
                  </div>
                  <div class="space-y-1">
                    <h3 class="text-xl font-semibold">Secure & Fast</h3>
                    <p class="text-gray-500">
                      Enhanced security features and optimized performance for a smooth experience.
                    </p>
                  </div>
                </div>
              </div>

              <div class="flex flex-wrap justify-start mt-10">
                <a href="#"
                  class="inline-flex items-center rounded-xl hover:opacity-90 transition-opacity duration-300">
                  <img src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png"
                    alt="Get it on Google Play" style="height: 55px; width: auto;">
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Special Supports Section -->
    <section class="py-20 bg-white relative overflow-hidden">
      <div
        class="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-b from-blue-100/30 to-transparent rounded-full blur-3xl opacity-40 -z-10">
      </div>
      <div
        class="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-t from-sky-100/30 to-transparent rounded-full blur-3xl opacity-40 -z-10">
      </div>

      <div class="container mx-auto px-4 md:px-6">
        <div class="text-center max-w-3xl mx-auto mb-16">
          <div
            class="inline-flex items-center justify-center px-4 py-1.5 rounded-full bg-gradient-to-r from-sky-500/10 to-blue-500/10 text-sm font-medium text-sky-700 mb-4">
            <span class="relative flex h-2 w-2 mr-2">
              <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-sky-400 opacity-75"></span>
              <span class="relative inline-flex rounded-full h-2 w-2 bg-sky-500"></span>
            </span>
            Customer Support
          </div>
          <h2
            class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 mb-6">
            Enjoy our Special Supports
          </h2>
          <p class="text-xl text-gray-600 mb-8">
            We're Happy to Help you Here!
          </p>
          <p class="text-gray-600">
            Have a question, need assistance, or want to share your feedback! Our team is ready to assist you. Get in
            touch with us through the contact details below, or fill out the contact us form on our official website,
            and we'll get back to you promptly.
          </p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
          <!-- Support Card 1 -->
          <div
            class="bg-white border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-500 rounded-xl overflow-hidden p-6 text-center group">
            <div
              class="flex h-16 w-16 mx-auto items-center justify-center rounded-full bg-gradient-to-r from-green-500/20 to-emerald-500/20 group-hover:from-green-500/30 group-hover:to-emerald-500/30 mb-6 transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="h-8 w-8 text-green-600">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-3 text-gray-900">Secure Payment</h3>
            <p class="text-gray-600">
              Multiple secure payment options with end-to-end encryption for all your transactions.
            </p>
          </div>

          <!-- Support Card 2 -->
          <div
            class="bg-white border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-500 rounded-xl overflow-hidden p-6 text-center group">
            <div
              class="flex h-16 w-16 mx-auto items-center justify-center rounded-full bg-gradient-to-r from-blue-500/20 to-indigo-500/20 group-hover:from-blue-500/30 group-hover:to-indigo-500/30 mb-6 transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="h-8 w-8 text-blue-600">
                <path d="M17 21v-2a4 4 0 0 1-4-4H5a4 4 0 0 1-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-3 text-gray-900">Refer & Earn</h3>
            <p class="text-gray-600">
              Invite friends and family to earn rewards when they join our platform.
            </p>
          </div>

          <!-- Support Card 3 -->
          <div
            class="bg-white border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-500 rounded-xl overflow-hidden p-6 text-center group">
            <div
              class="flex h-16 w-16 mx-auto items-center justify-center rounded-full bg-gradient-to-r from-amber-500/20 to-yellow-500/20 group-hover:from-amber-500/30 group-hover:to-yellow-500/30 mb-6 transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="h-8 w-8 text-amber-600">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-3 text-gray-900">Trust Pay</h3>
            <p class="text-gray-600">
              Our Trust Pay system ensures your money is safe until you receive your product or service.
            </p>
          </div>

          <!-- Support Card 4 -->
          <div
            class="bg-white border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-500 rounded-xl overflow-hidden p-6 text-center group">
            <div
              class="flex h-16 w-16 mx-auto items-center justify-center rounded-full bg-gradient-to-r from-purple-500/20 to-violet-500/20 group-hover:from-purple-500/30 group-hover:to-violet-500/30 mb-6 transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="h-8 w-8 text-purple-600">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-3 text-gray-900">24X7 Support</h3>
            <p class="text-gray-600">
              Our dedicated support team is available round the clock to assist you with any issues.
            </p>
          </div>
        </div>

        <div class="mt-16 text-center">
          <a href="#contact"
            class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white rounded-xl shadow-lg shadow-blue-500/20 hover:shadow-xl hover:shadow-blue-500/30 transition-all duration-300 font-medium text-lg">
            Contact Support
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="ml-2 h-5 w-5">
              <line x1="5" y1="12" x2="19" y2="12"></line>
              <polyline points="12 5 19 12 12 19"></polyline>
            </svg>
          </a>
        </div>
      </div>
    </section>

    <!-- Refer & Earn Section -->
    <section id="refer-earn" class="py-24 relative overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-gray-50 to-white -z-10"></div>
      <div
        class="absolute top-1/2 left-1/2 w-full h-full max-w-6xl -translate-x-1/2 -translate-y-1/2 bg-gradient-to-r from-sky-100/40 to-blue-100/40 rounded-[40px] blur-3xl opacity-60 -z-10 transform rotate-12">
      </div>

      <div class="container mx-auto px-4 md:px-6">
        <div class="grid gap-12 md:grid-cols-2 md:gap-16 items-center">
          <div class="order-2 md:order-1">
            <div class="space-y-4 md:order-2">
              <div
                class="inline-flex items-center justify-center px-4 py-1.5 rounded-full bg-gradient-to-r from-sky-500/10 to-blue-500/10 text-sm font-medium text-sky-700 mb-4">
                <span class="relative flex h-2 w-2 mr-2">
                  <span
                    class="animate-ping absolute inline-flex h-full w-full rounded-full bg-sky-400 opacity-75"></span>
                  <span class="relative inline-flex rounded-full h-2 w-2 bg-sky-500"></span>
                </span>
                Refer & Earn Program
              </div>
              <h2
                class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900">
                Share & Earn Rewards
              </h2>
              <p class="text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Invite your friends and family to Netvis Solution and earn rewards when they join. It's simple, fast,
                and mutually beneficial!
              </p>
              <div class="space-y-6 mt-8">
                <div class="flex items-start gap-4 group">
                  <div
                    class="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-gradient-to-r from-sky-500/20 to-blue-500/20 text-sky-600 group-hover:from-sky-500/30 group-hover:to-blue-500/30 transition-all duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="h-6 w-6">
                      <path d="M17 21v-2a4 4 0 0 1-4-4H5a4 4 0 0 1-4 4v2"></path>
                      <circle cx="9" cy="7" r="4"></circle>
                      <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                      <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                  </div>
                  <div class="space-y-1">
                    <h3 class="text-xl font-semibold">Invite Friends</h3>
                    <p class="text-gray-500">
                      Share your unique referral link with friends and family through social media or messaging apps.
                    </p>
                  </div>
                </div>
                <div class="flex items-start gap-4 group">
                  <div
                    class="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-gradient-to-r from-sky-500/20 to-blue-500/20 text-sky-600 group-hover:from-sky-500/30 group-hover:to-blue-500/30 transition-all duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="h-6 w-6">
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                      <polyline points="12 5 19 12 12 19"></polyline>
                    </svg>
                  </div>
                  <div class="space-y-1">
                    <h3 class="text-xl font-semibold">Friends Sign Up</h3>
                    <p class="text-gray-500">
                      When your friends sign up using your referral link, they get exclusive benefits and cashback
                      offers.
                    </p>
                  </div>
                </div>
                <div class="flex items-start gap-4 group">
                  <div
                    class="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-gradient-to-r from-sky-500/20 to-blue-500/20 text-sky-600 group-hover:from-sky-500/30 group-hover:to-blue-500/30 transition-all duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="h-6 w-6">
                      <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                      <line x1="1" y1="10" x2="23" y2="10"></line>
                    </svg>
                  </div>
                  <div class="space-y-1">
                    <h3 class="text-xl font-semibold">Earn Rewards</h3>
                    <p class="text-gray-500">
                      You earn rewards for every successful referral. The more friends join, the more you earn!
                    </p>
                  </div>
                </div>
              </div>
              <div class="mt-10">
                <button
                  class="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white px-6 py-3 rounded-md font-medium text-base shadow-md shadow-blue-500/20 transition-all hover:shadow-lg hover:shadow-blue-500/30">
                  Get Started & Earn
                </button>
              </div>
            </div>
          </div>
          <div class="relative md:order-1">
            <div
              class="absolute inset-0 bg-gradient-to-r from-sky-500/20 to-blue-500/20 rounded-3xl blur-3xl opacity-30 -z-10 transform -rotate-6">
            </div>
            <div class="relative bg-white rounded-3xl p-2 shadow-2xl shadow-blue-500/10 overflow-hidden">
              <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-sky-50 to-blue-50 opacity-50">
              </div>
              <img
                src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                alt="Refer and Earn" class="w-full h-full object-cover rounded-2xl relative z-10" />
              <div class="absolute inset-0 bg-gradient-to-t from-blue-600/40 to-transparent rounded-2xl z-20"></div>
              <div class="absolute bottom-6 left-6 right-6 text-white z-30">
                <h3 class="text-2xl font-bold mb-2">Share with Friends</h3>
                <p class="text-white/90">Invite friends and earn rewards together</p>
              </div>
            </div>
            <div
              class="absolute -bottom-6 -right-6 w-24 h-24 bg-gradient-to-br from-sky-500 to-blue-600 rounded-full blur-2xl opacity-20">
            </div>
            <div
              class="absolute -top-6 -left-6 w-24 h-24 bg-gradient-to-br from-sky-300 to-blue-400 rounded-full blur-2xl opacity-20">
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="py-24 relative overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-sky-500 to-blue-600 -z-10"></div>
      <div class="absolute top-0 right-0 w-1/2 h-full bg-white/5 -z-10 transform skew-x-12"></div>
      <div class="absolute bottom-0 left-0 w-1/2 h-1/2 bg-white/5 -z-10 transform -skew-x-12"></div>
      <div class="absolute top-0 left-0 w-full h-full opacity-10 -z-10"
        style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHBhdGggZD0iTTAgMEgxVjEwMEgwVjBaIiBmaWxsPSJ3aGl0ZSIvPgogIDxwYXRoIGQ9Ik05OSAwSDEwMFYxMDBIOTlWMFoiIGZpbGw9IndoaXRlIi8+CiAgPHBhdGggZD0iTTAgMEgxMDBWMUgwVjBaIiBmaWxsPSJ3aGl0ZSIvPgogIDxwYXRoIGQ9Ik0wIDk5SDEwMFYxMDBIMFY5OVoiIGZpbGw9IndoaXRlIi8+CiAgPHBhdGggZD0iTTQ5LjUgMEg1MC41VjEwMEg0OS41VjBaIiBmaWxsPSJ3aGl0ZSIvPgogIDxwYXRoIGQ9Ik0wIDQ5LjVIMTAwVjUwLjVIMFY0OS41WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg=='); background-repeat: repeat;">
      </div>

      <div class="container mx-auto px-4 md:px-6 relative">
        <div class="text-center space-y-4 mb-16">
          <div
            class="inline-flex items-center justify-center px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-sm text-sm font-medium text-white mb-4">
            <span class="relative flex h-2 w-2 mr-2">
              <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75"></span>
              <span class="relative inline-flex rounded-full h-2 w-2 bg-white"></span>
            </span>
            Simple Process
          </div>
          <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-white">How It Works</h2>
          <p class="mx-auto max-w-[700px] text-gray-100 md:text-xl/relaxed">
            We've made it simple for you to shop, save money, and earn cashbacks with our platform. Follow these
            easy steps:
          </p>
        </div>

        <div class="tabs w-full">
          <div class="flex justify-center mb-8 px-4">
            <div
              class="bg-white/10 backdrop-blur-sm border border-white/20 p-1 rounded-md overflow-x-auto max-w-full whitespace-nowrap touch-pan-x">
              <div class="inline-flex gap-2">
                <button
                  class="tab-trigger px-4 py-3 min-h-[48px] min-w-[48px] rounded-md bg-white text-blue-600 shadow-lg text-sm sm:text-base transition-all duration-300 hover:bg-blue-600 hover:text-white active:bg-blue-700 touch-manipulation"
                  data-tab="tab1">
                  <span class="relative z-10">Step 1: Choose</span>
                </button>
                <button
                  class="tab-trigger px-4 py-3 min-h-[48px] min-w-[48px] rounded-md text-white text-sm sm:text-base transition-all duration-300 hover:bg-blue-500 hover:text-white hover:shadow-md active:bg-blue-700 touch-manipulation"
                  data-tab="tab2">
                  <span class="relative z-10">Step 2: Find</span>
                </button>
                <button
                  class="tab-trigger px-4 py-3 min-h-[48px] min-w-[48px] rounded-md text-white text-sm sm:text-base transition-all duration-300 hover:bg-blue-500 hover:text-white hover:shadow-md active:bg-blue-700 touch-manipulation"
                  data-tab="tab3">
                  <span class="relative z-10">Step 3: Earn</span>
                </button>
              </div>
            </div>
          </div>

          <div class="mt-4 overflow-hidden rounded-3xl bg-white shadow-2xl shadow-blue-950/20">
            <div id="tab1" class="tab-content active">
              <div class="grid md:grid-cols-2 grid-cols-1">
                <!-- Content shows first on mobile, image second (reverse order from md screens) -->
                <div class="md:order-2 order-1 flex flex-col justify-center p-6 sm:p-8 md:p-12">
                  <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">Choose what to do?</h3>
                  <p class="text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base">
                    Browse through our wide range of products and services. Whether you want to shop online, pay
                    bills, book travel tickets, or buy insurance, we've got you covered.
                  </p>
                  <ul class="space-y-4 mb-6 sm:mb-8">
                    <li class="flex items-start sm:items-center gap-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="h-6 w-6 text-green-500 flex-shrink-0 mt-0.5 sm:mt-0">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <span class="text-gray-700">Shop from thousands of products</span>
                    </li>
                    <li class="flex items-start sm:items-center gap-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="h-6 w-6 text-green-500 flex-shrink-0 mt-0.5 sm:mt-0">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <span class="text-gray-700">Pay bills and recharge services</span>
                    </li>
                    <li class="flex items-start sm:items-center gap-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="h-6 w-6 text-green-500 flex-shrink-0 mt-0.5 sm:mt-0">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <span class="text-gray-700">Book travel tickets and accommodations</span>
                    </li>
                  </ul>
                  <button
                    class="w-full sm:w-fit bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white px-6 py-3 rounded-md shadow-md shadow-blue-500/20 hover:shadow-lg hover:shadow-blue-500/30 transition-all duration-300 min-h-[48px] touch-manipulation">
                    Start Exploring
                  </button>
                </div>
                <!-- Image shows second on mobile, first on larger screens -->
                <div class="md:order-1 order-2 aspect-square md:aspect-auto relative overflow-hidden py-4 md:py-0">
                  <div class="absolute inset-0 bg-gradient-to-br from-sky-100 to-blue-100 opacity-30"></div>
                  <svg width="600" height="600" viewBox="0 0 600 600" fill="none" xmlns="http://www.w3.org/2000/svg"
                    class="h-full w-full object-contain md:object-cover relative z-10 p-4 md:p-0">
                    <rect x="150" y="150" width="300" height="300" rx="20" fill="#0EA5E9" fill-opacity="0.2"
                      stroke="#0EA5E9" stroke-width="4" />
                    <rect x="180" y="200" width="240" height="40" rx="8" fill="#0EA5E9" />
                    <rect x="180" y="260" width="240" height="20" rx="4" fill="#E5E7EB" />
                    <rect x="180" y="300" width="240" height="20" rx="4" fill="#E5E7EB" />
                    <rect x="180" y="340" width="240" height="20" rx="4" fill="#E5E7EB" />
                    <rect x="180" y="380" width="120" height="40" rx="8" fill="#3B82F6" />
                    <path d="M210 400L220 410L250 380" stroke="white" stroke-width="3" stroke-linecap="round"
                      stroke-linejoin="round" />
                  </svg>
                </div>
              </div>
            </div>

            <div id="tab2" class="tab-content">
              <div class="grid md:grid-cols-2 grid-cols-1">
                <!-- Content shows first on mobile, image second (reverse order from md screens) -->
                <div class="md:order-2 order-1 flex flex-col justify-center p-6 sm:p-8 md:p-12">
                  <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">Find what you want</h3>
                  <p class="text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base">
                    Our user-friendly interface makes it easy to search and find exactly what you're looking for.
                    Use filters to narrow down your choices and find the best deals.
                  </p>
                  <ul class="space-y-4 mb-6 sm:mb-8">
                    <li class="flex items-start sm:items-center gap-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="h-6 w-6 text-green-500 flex-shrink-0 mt-0.5 sm:mt-0">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <span class="text-gray-700">Easy-to-use search functionality</span>
                    </li>
                    <li class="flex items-start sm:items-center gap-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="h-6 w-6 text-green-500 flex-shrink-0 mt-0.5 sm:mt-0">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <span class="text-gray-700">Advanced filtering options</span>
                    </li>
                    <li class="flex items-start sm:items-center gap-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="h-6 w-6 text-green-500 flex-shrink-0 mt-0.5 sm:mt-0">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <span class="text-gray-700">Compare products and services</span>
                    </li>
                  </ul>
                  <button
                    class="w-full sm:w-fit bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white px-6 py-3 rounded-md shadow-md shadow-blue-500/20 hover:shadow-lg hover:shadow-blue-500/30 transition-all duration-300 min-h-[48px] touch-manipulation">
                    Search Now
                  </button>
                </div>
                <!-- Image shows second on mobile, first on larger screens -->
                <div class="md:order-1 order-2 aspect-square md:aspect-auto relative overflow-hidden py-4 md:py-0">
                  <div class="absolute inset-0 bg-gradient-to-br from-sky-100 to-blue-100 opacity-30"></div>
                  <svg width="600" height="600" viewBox="0 0 600 600" fill="none" xmlns="http://www.w3.org/2000/svg"
                    class="h-full w-full object-contain md:object-cover relative z-10 p-4 md:p-0">
                    <rect x="150" y="150" width="300" height="300" rx="20" fill="#0EA5E9" fill-opacity="0.2"
                      stroke="#0EA5E9" stroke-width="4" />
                    <rect x="180" y="200" width="240" height="40" rx="8" fill="#0EA5E9" />
                    <circle cx="200" cy="220" r="10" fill="white" />
                    <path d="M200 215V225" stroke="#0EA5E9" stroke-width="2" stroke-linecap="round" />
                    <path d="M195 220H205" stroke="#0EA5E9" stroke-width="2" stroke-linecap="round" />
                    <rect x="180" y="260" width="100" height="160" rx="8" fill="#E5E7EB" />
                    <rect x="300" y="260" width="120" height="160" rx="8" fill="#E5E7EB" />
                    <rect x="200" y="280" width="60" height="10" rx="2" fill="white" />
                    <rect x="200" y="300" width="60" height="10" rx="2" fill="white" />
                    <rect x="200" y="320" width="60" height="10" rx="2" fill="white" />
                    <rect x="320" y="280" width="80" height="10" rx="2" fill="white" />
                    <rect x="320" y="300" width="80" height="10" rx="2" fill="white" />
                    <rect x="320" y="320" width="80" height="10" rx="2" fill="white" />
                  </svg>
                </div>
              </div>
            </div>

            <div id="tab3" class="tab-content">
              <div class="grid md:grid-cols-2 grid-cols-1">
                <!-- Content shows first on mobile, image second (reverse order from md screens) -->
                <div class="md:order-2 order-1 flex flex-col justify-center p-6 sm:p-8 md:p-12">
                  <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">Enjoy amazing cashback</h3>
                  <p class="text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base">
                    Get up to 25% cashback on all your purchases. The cashback is credited directly to your Netvis
                    wallet, which you can use for future transactions.
                  </p>
                  <ul class="space-y-4 mb-6 sm:mb-8">
                    <li class="flex items-start sm:items-center gap-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="h-6 w-6 text-green-500 flex-shrink-0 mt-0.5 sm:mt-0">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <span class="text-gray-700">Up to 25% cashback on purchases</span>
                    </li>
                    <li class="flex items-start sm:items-center gap-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="h-6 w-6 text-green-500 flex-shrink-0 mt-0.5 sm:mt-0">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <span class="text-gray-700">Easy wallet redemption</span>
                    </li>
                    <li class="flex items-start sm:items-center gap-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="h-6 w-6 text-green-500 flex-shrink-0 mt-0.5 sm:mt-0">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <span class="text-gray-700">Exclusive cashback offers for loyal users</span>
                    </li>
                  </ul>
                  <button
                    class="w-full sm:w-fit bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white px-6 py-3 rounded-md shadow-md shadow-blue-500/20 hover:shadow-lg hover:shadow-blue-500/30 transition-all duration-300 min-h-[48px] touch-manipulation">
                    Start Earning
                  </button>
                </div>
                <!-- Image shows second on mobile, first on larger screens -->
                <div class="md:order-1 order-2 aspect-square md:aspect-auto relative overflow-hidden py-4 md:py-0">
                  <div class="absolute inset-0 bg-gradient-to-br from-sky-100 to-blue-100 opacity-30"></div>
                  <svg width="600" height="600" viewBox="0 0 600 600" fill="none" xmlns="http://www.w3.org/2000/svg"
                    class="h-full w-full object-contain md:object-cover relative z-10 p-4 md:p-0">
                    <circle cx="300" cy="300" r="150" fill="#0EA5E9" fill-opacity="0.2" stroke="#0EA5E9"
                      stroke-width="4" />
                    <path d="M250 300H350" stroke="#0EA5E9" stroke-width="4" stroke-linecap="round" />
                    <path d="M300 250V350" stroke="#0EA5E9" stroke-width="4" stroke-linecap="round" />
                    <circle cx="300" cy="300" r="50" fill="#3B82F6" />
                    <path d="M280 300H320" stroke="white" stroke-width="4" stroke-linecap="round" />
                    <path d="M290 290H310" stroke="white" stroke-width="4" stroke-linecap="round" />
                    <path d="M290 310H310" stroke="white" stroke-width="4" stroke-linecap="round" />
                    <path d="M200 200L220 220" stroke="#0EA5E9" stroke-width="4" stroke-linecap="round" />
                    <path d="M400 200L380 220" stroke="#0EA5E9" stroke-width="4" stroke-linecap="round" />
                    <path d="M200 400L220 380" stroke="#0EA5E9" stroke-width="4" stroke-linecap="round" />
                    <path d="M400 400L380 380" stroke="#0EA5E9" stroke-width="4" stroke-linecap="round" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="py-20 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
      <!-- Background elements -->
      <div
        class="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-b from-blue-100/30 to-transparent rounded-full blur-3xl">
      </div>
      <div
        class="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-t from-indigo-100/30 to-transparent rounded-full blur-3xl">
      </div>

      <div class="container mx-auto px-4 md:px-6 relative z-10">
        <div class="text-center max-w-3xl mx-auto mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
          <p class="text-gray-600 text-lg">Find answers to common questions about our services and platform</p>
        </div>

        <div class="max-w-3xl mx-auto">
          <!-- FAQ Accordion -->
          <div class="space-y-5">
            <!-- FAQ Item 1 -->
            <div
              class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
              <button
                class="accordion-trigger w-full flex justify-between items-center p-5 text-left font-medium text-gray-900">
                <span class="text-lg">What is the Refer and Earn program?</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="h-5 w-5 text-blue-600 transition-transform">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </button>
              <div class="accordion-content bg-white px-5 pb-5">
                <p class="text-gray-600">
                  The Refer and Earn program allows you to invite friends to use our platform.
                  When they sign up using your referral link or code and meet the eligibility
                  criteria, both you and your friend can earn rewards.
                </p>
              </div>
            </div>

            <!-- FAQ Item 2 -->
            <div
              class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
              <button
                class="accordion-trigger w-full flex justify-between items-center p-5 text-left font-medium text-gray-900">
                <span class="text-lg">How can I refer a friend?</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="h-5 w-5 text-blue-600 transition-transform">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </button>
              <div class="accordion-content bg-white px-5 pb-5">
                <p class="text-gray-600">
                  You can refer a friend by sharing your unique referral link or code with them.
                  This can be done through email, social media, or messaging apps. Your friend
                  needs to use this link or code when signing up to ensure you both receive the rewards.
                </p>
              </div>
            </div>

            <!-- FAQ Item 3 -->
            <div
              class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
              <button
                class="accordion-trigger w-full flex justify-between items-center p-5 text-left font-medium text-gray-900">
                <span class="text-lg">What benefit can I get?</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="h-5 w-5 text-blue-600 transition-transform">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </button>
              <div class="accordion-content bg-white px-5 pb-5">
                <p class="text-gray-600">
                  When your referred friend completes their first transaction, you'll receive reward points
                  or cashback that can be used for future purchases. The exact benefit amount may vary
                  based on ongoing promotions and your friend's transaction value.
                </p>
              </div>
            </div>

            <!-- FAQ Item 4 -->
            <div
              class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
              <button
                class="accordion-trigger w-full flex justify-between items-center p-5 text-left font-medium text-gray-900">
                <span class="text-lg">Is there a limit to how many friends I can refer?</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="h-5 w-5 text-blue-600 transition-transform">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </button>
              <div class="accordion-content bg-white px-5 pb-5">
                <p class="text-gray-600">
                  There is no limit to the number of friends you can refer. The more friends you refer who
                  complete qualifying transactions, the more rewards you can earn. This makes our referral
                  program a great way to earn additional benefits while helping your friends discover our services.
                </p>
              </div>
            </div>

            <!-- FAQ Item 5 -->
            <div
              class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
              <button
                class="accordion-trigger w-full flex justify-between items-center p-5 text-left font-medium text-gray-900">
                <span class="text-lg">How do I track my referrals?</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="h-5 w-5 text-blue-600 transition-transform">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </button>
              <div class="accordion-content bg-white px-5 pb-5">
                <p class="text-gray-600">
                  You can track all your referrals and earned rewards in the "My Referrals" section of your account
                  dashboard.
                  This section provides detailed information about pending and completed referrals, as well as the
                  rewards
                  you've earned and when they'll be available for use.
                </p>
              </div>
            </div>
          </div>

          <!-- More Questions CTA -->
          <div class="mt-10 text-center">
            <p class="text-gray-600 mb-4">Still have questions? We're here to help.</p>
            <a href="#contact"
              class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-300">
              Contact Support
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                  d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12 10.586H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                  clip-rule="evenodd" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- JavaScript for FAQ Accordion -->
    <script>
      // Initialize accordion functionality
      document.addEventListener('DOMContentLoaded', function () {
        initFaqAccordions();
      });

      function initFaqAccordions() {
        try {
          // Remove all existing event listeners
          const oldFaq = document.querySelector('#faq');
          if (oldFaq) {
            const newFaq = oldFaq.cloneNode(true);
            if (oldFaq.parentNode) {
              oldFaq.parentNode.replaceChild(newFaq, oldFaq);
            }
          }

          // Get fresh references
          const accordionItems = document.querySelectorAll('#faq .accordion-trigger');
          const accordionContents = document.querySelectorAll('#faq .accordion-content');

          if (!accordionItems.length) return;

          // Reset all content styling first to ensure clean state
          accordionContents.forEach(content => {
            // Make sure we remove any inline styles that might be interfering
            content.style.removeProperty('max-height');
            content.style.removeProperty('opacity');
            content.style.removeProperty('transform');
            content.style.removeProperty('visibility');
            content.style.overflow = 'hidden';

            // Initially hide all
            content.style.display = 'none';
          });

          // Reset all arrows
          accordionItems.forEach(item => {
            const arrow = item.querySelector('svg');
            if (arrow) arrow.style.transform = 'rotate(0deg)';
          });

          // Show the first item by default
          if (accordionContents.length > 0) {
            const firstContent = accordionContents[0];
            firstContent.style.display = 'block';
            firstContent.style.opacity = '1';
            firstContent.style.visibility = 'visible';

            if (accordionItems.length > 0) {
              accordionItems[0].classList.add('active');
              const firstArrow = accordionItems[0].querySelector('svg');
              if (firstArrow) firstArrow.style.transform = 'rotate(180deg)';
            }
          }

          // Add click event to each accordion item
          accordionItems.forEach((item, index) => {
            item.onclick = function (e) {
              e.preventDefault();

              const content = accordionContents[index];
              const isActive = this.classList.contains('active');

              // Close all items
              accordionItems.forEach(i => {
                i.classList.remove('active');
                const arrow = i.querySelector('svg');
                if (arrow) arrow.style.transform = 'rotate(0deg)';
              });

              accordionContents.forEach(c => {
                c.style.display = 'none';
                c.style.opacity = '0';
                c.style.visibility = 'hidden';
              });

              // If the clicked item wasn't active, open it
              if (!isActive) {
                this.classList.add('active');
                const arrow = this.querySelector('svg');
                if (arrow) arrow.style.transform = 'rotate(180deg)';

                if (content) {
                  // Ensure full visibility with all necessary CSS properties
                  content.style.display = 'block';
                  content.style.opacity = '1';
                  content.style.visibility = 'visible';
                  content.style.maxHeight = 'none';
                  content.style.overflow = 'visible';
                }
              }
            };
          });

          console.log('FAQ Accordion initialization complete - simplified version');
        } catch (error) {
          console.error('Error initializing FAQ accordions:', error);
        }
      }
    </script>

    <!-- Testimonials Section -->
    <section class="py-20 bg-white relative overflow-hidden">
      <div
        class="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-b from-blue-50 to-transparent rounded-full blur-3xl opacity-70 -z-10">
      </div>
      <div
        class="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-t from-indigo-50 to-transparent rounded-full blur-3xl opacity-70 -z-10">
      </div>

      <div class="container mx-auto px-4 md:px-6">
        <div class="text-center max-w-3xl mx-auto mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">What Our Customers Say</h2>
          <p class="text-gray-600 text-lg">Discover why thousands of customers trust us for their shopping needs</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <!-- Testimonial 1 -->
          <div
            class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
            <div class="flex items-center mb-4">
              <div
                class="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold text-xl mr-4">
                RK
              </div>
              <div>
                <h4 class="font-semibold text-gray-900">Rahul Kumar</h4>
                <div class="flex text-yellow-400">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                    class="w-4 h-4 text-gray-300">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
            <p class="text-gray-600">
              "I've been using Netvis for all my shopping needs for over a year now. The variety of products,
              competitive prices, and quick delivery make it my go-to platform. The cashback on every purchase is a
              great bonus!"
            </p>
          </div>

          <!-- Testimonial 2 -->
          <div
            class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
            <div class="flex items-center mb-4">
              <div
                class="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 font-bold text-xl mr-4">
                PS
              </div>
              <div>
                <h4 class="font-semibold text-gray-900">Priya Sharma</h4>
                <div class="flex text-yellow-400">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                    class="w-4 h-4 text-gray-300">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
            <p class="text-gray-600">
              "The Ayurvedic products available on Netvis are authentic and of high quality. I've tried several brands,
              and I'm impressed with the curation. The refer and earn program is also quite generous!"
            </p>
          </div>

          <!-- Testimonial 3 -->
          <div
            class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
            <div class="flex items-center mb-4">
              <div
                class="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center text-green-600 font-bold text-xl mr-4">
                AM
              </div>
              <div>
                <h4 class="font-semibold text-gray-900">Amit Mehta</h4>
                <div class="flex text-yellow-400">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                    class="w-4 h-4 text-gray-300">
                    <path fill-rule="evenodd"
                      d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                      clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
            <p class="text-gray-600">
              "The bill payment and recharge services are incredibly convenient. I've saved a lot of time by managing
              all my utility payments through a single platform. Customer support is also very responsive."
            </p>
          </div>
        </div>


      </div>
    </section>



    <!-- Footer -->
    <footer class="bg-gray-900 text-gray-300 relative overflow-hidden">
      <div class="absolute top-0 left-0 w-full h-full opacity-5"
        style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHBhdGggZD0iTTAgMEgxVjEwMEgwVjBaIiBmaWxsPSJ3aGl0ZSIvPgogIDxwYXRoIGQ9Ik05OSAwSDEwMFYxMDBIOTlWMFoiIGZpbGw9IndoaXRlIi8+CiAgPHBhdGggZD0iTTAgMEgxMDBWMUgwVjBaIiBmaWxsPSJ3aGl0ZSIvPgogIDxwYXRoIGQ9Ik0wIDk5SDEwMFYxMDBIMFY5OVoiIGZpbGw9IndoaXRlIi8+CiAgPHBhdGggZD0iTTQ5LjUgMEg1MC41VjEwMEg0OS41VjBaIiBmaWxsPSJ3aGl0ZSIvPgogIDxwYXRoIGQ9Ik0wIDQ5LjVIMTAwVjUwLjVIMFY0OS41WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg=='); background-repeat: repeat;">
      </div>
      <div
        class="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-b from-sky-500/10 to-transparent rounded-full blur-3xl opacity-30 transform translate-x-1/2 -translate-y-1/2">
      </div>
      <div
        class="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-t from-blue-500/10 to-transparent rounded-full blur-3xl opacity-30 transform -translate-x-1/2 translate-y-1/2">
      </div>

      <div class="container mx-auto px-4 md:px-6 py-16 relative">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          <!-- Column 1 - Logo & Social -->
          <div>
            <a href="/" class="flex items-center mb-6">
              <img src="public/logo.webp" alt="Netvis Solution Logo" class="h-10 w-auto">
            </a>
            <p class="mb-6">Shop Now & Save Money!</p>
            <div class="flex space-x-4">
              <a href="#"
                class="bg-gray-800 hover:bg-blue-600 h-10 w-10 rounded-full flex items-center justify-center transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                </svg>
              </a>
              <a href="#"
                class="bg-gray-800 hover:bg-blue-400 h-10 w-10 rounded-full flex items-center justify-center transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                  <path
                    d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z">
                  </path>
                </svg>
              </a>
              <a href="#"
                class="bg-gray-800 hover:bg-pink-600 h-10 w-10 rounded-full flex items-center justify-center transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                  <rect x="2" y="2" width="20" height="20" rx="2" ry="2"></rect>
                  <line x1="12" y1="18" x2="12.01" y2="18"></line>
                </svg>
              </a>
            </div>
          </div>

          <!-- Column 2 - Quick Links -->
          <div>
            <h4 class="text-lg font-bold mb-6 text-white">Quick Links</h4>
            <ul class="space-y-3">
              <li>
                <a href="#" class="hover:text-white transition-colors duration-300 flex items-center group">
                  <span
                    class="w-0 h-0.5 bg-sky-500 mr-0 group-hover:w-2 group-hover:mr-2 transition-all duration-300"></span>
                  Home
                </a>
              </li>
              <li>
                <a href="#" class="hover:text-white transition-colors duration-300 flex items-center group">
                  <span
                    class="w-0 h-0.5 bg-sky-500 mr-0 group-hover:w-2 group-hover:mr-2 transition-all duration-300"></span>
                  About Us
                </a>
              </li>
              <li>
                <a href="#" class="hover:text-white transition-colors duration-300 flex items-center group">
                  <span
                    class="w-0 h-0.5 bg-sky-500 mr-0 group-hover:w-2 group-hover:mr-2 transition-all duration-300"></span>
                  Our Products & Services
                </a>
              </li>
              <li>
                <a href="#" class="hover:text-white transition-colors duration-300 flex items-center group">
                  <span
                    class="w-0 h-0.5 bg-sky-500 mr-0 group-hover:w-2 group-hover:mr-2 transition-all duration-300"></span>
                  Our Blog
                </a>
              </li>
              <li>
                <a href="#" class="hover:text-white transition-colors duration-300 flex items-center group">
                  <span
                    class="w-0 h-0.5 bg-sky-500 mr-0 group-hover:w-2 group-hover:mr-2 transition-all duration-300"></span>
                  Contact
                </a>
              </li>
            </ul>
          </div>

          <!-- Column 3 - Contact Info -->
          <div>
            <h4 class="text-lg font-bold mb-6 text-white">Contact Us</h4>
            <ul class="space-y-4">
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="h-5 w-5 text-sky-400 mt-1 mr-3 shrink-0">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
                <span>
                  123 Main Street, Suite 500
                  <br />
                  New Delhi, Delhi 110001
                  <br />
                  India
                </span>
              </li>
              <li class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="h-5 w-5 text-sky-400 mr-3 shrink-0">
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                  </path>
                </svg>
                <span>+91 98765 43210</span>
              </li>
              <li class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="h-5 w-5 text-sky-400 mr-3 shrink-0">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <span><EMAIL></span>
              </li>
              <li class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="h-5 w-5 text-sky-400 mr-3 shrink-0">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                <span>Mon - Sat: 10:00 AM - 7:00 PM</span>
              </li>
            </ul>
          </div>

          <!-- Column 4 - Google Map -->
          <div>
            <h4 class="text-lg font-bold mb-6 text-white">Find Us</h4>
            <div class="h-56 bg-gray-800 rounded-xl overflow-hidden border border-gray-700">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3501.678016537032!2d77.2089979!3d28.6139391!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x390cfd5b347eb62d%3A0x52c2b7494e204dce!2sNew%20Delhi%2C%20Delhi!5e0!3m2!1sen!2sin!4v1715284055915!5m2!1sen!2sin"
                width="100%" height="100%" style="border: 0" allowfullscreen loading="lazy"
                referrerpolicy="no-referrer-when-downgrade" title="Google Map"></iframe>
            </div>
          </div>
        </div>

        <!-- Bottom Bar -->
        <div class="pt-6 border-t border-gray-800 text-center">
          <p class="text-gray-400"> 2025 Netvis Solution Pvt Ltd. All Rights Reserved.</p>
        </div>
      </div>
    </footer>
  </main>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // --- DATA ---
      const heroSlides = [
        {
          tag: "E-Books",
          title: "E Books Bundle",
          description: "Discover a world of knowledge with our extensive E-Book collection. Save up to 75% on premium titles.",
          buttonText: "Explore Now",
          image: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1521587760476-6c12a4b040da?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Shopping",
          title: "Online Shopping",
          description: "Shop from thousands of products with amazing discounts and cashback offers.",
          buttonText: "Shop Now",
          image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Payments",
          title: "Bill Payments",
          description: "Pay all your bills in one place and earn cashback on every transaction.",
          buttonText: "Pay Bills",
          image: "https://images.unsplash.com/photo-1556742502-ec7c0e9f34b1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1580577665996-613f3f3373c2?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Travel",
          title: "Travel Booking",
          description: "Book flights, hotels, buses, and more with exclusive discounts.",
          buttonText: "Book Now",
          image: "https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1476514525535-07fb3b4ae5f1?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Utilities",
          title: "Mobile Recharge",
          description: "Quick and easy recharge options for all your mobile and data needs.",
          buttonText: "Recharge",
          image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1614332287897-cdc485fa562d?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Security",
          title: "Insurance",
          description: "Secure your future with our comprehensive insurance solutions.",
          buttonText: "Get Insured",
          image: "https://images.unsplash.com/photo-1450101499163-c8848c66ca85?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1542838132-92c53300491e?q=80&w=1974&auto=format&fit=crop"
        },
        {
          tag: "Rewards",
          title: "Refer & Earn",
          description: "Invite your friends and family to earn rewards when they join our platform.",
          buttonText: "Refer Now",
          image: "https://images.unsplash.com/photo-1559526324-4b87b5e36e44?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1521791136064-7986c2920216?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Deals",
          title: "Cashback Offers",
          description: "Get up to 25% cashback on all your purchases across our platform.",
          buttonText: "View Offers",
          image: "https://images.unsplash.com/photo-1579621970795-87f54f59740f?q=80&w=2070&auto=format&fit=crop",
          backgroundImage: "https://images.unsplash.com/photo-1561414927-6d86591d0c4f?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Support",
          title: "24/7 Support",
          description: "Our dedicated support team is available round the clock to assist you.",
          buttonText: "Contact Us",
          image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1587560699334-cc426240169f?q=80&w=2070&auto=format&fit=crop"
        }
      ];

      // --- STATE ---
      let currentSlide = 0;
      let autoSlideInterval;
      const autoSlideDelay = 6000;

      // --- DOM ELEMENTS ---
      const heroSection = document.getElementById('hero-section');
      const bgContainer = document.getElementById('hero-bg-container');
      const prevButton = document.getElementById('prev-slide');
      const nextButton = document.getElementById('next-slide');
      const indicatorsContainer = document.getElementById('hero-indicators');

      const heroTextContainer = document.querySelector('#hero-text .animate-on-change');
      const heroTag = heroTextContainer?.querySelector('span');
      const heroTitle = heroTextContainer?.querySelector('h1');
      const heroDesc = heroTextContainer?.querySelector('p');
      const heroButton = heroTextContainer?.querySelector('button');
      const heroImageContainer = document.getElementById('hero-image-container');
      const heroPoster = document.getElementById('hero-poster');

      // --- FUNCTIONS ---

      /**
       * Generates indicator buttons for each slide.
       */
      function createIndicators() {
        indicatorsContainer.innerHTML = '';
        heroSlides.forEach((_, index) => {
          const button = document.createElement('button');
          button.classList.add('w-2', 'h-2', 'sm:w-2.5', 'sm:h-2.5', 'rounded-full', 'transition-all', 'duration-300', 'touch-manipulation', 'bg-white/40', 'hover:bg-white/60');
          button.dataset.index = index;
          // Add minimum touch target size for mobile
          button.style.minWidth = '28px';
          button.style.minHeight = '28px';
          button.style.display = 'flex';
          button.style.alignItems = 'center';
          button.style.justifyContent = 'center';
          indicatorsContainer.appendChild(button);
          button.addEventListener('click', () => {
            updateSlide(index);
            resetAutoSlide();
          });
        });
      }

      /**
       * Main function to update the slide content and visuals.
       */
      function updateSlide(index) {
        currentSlide = index;
        const slideData = heroSlides[index];

        // 1. Cross-fade background image
        const newBg = document.createElement('img');
        newBg.src = slideData.backgroundImage;
        newBg.alt = slideData.title + " background";
        newBg.className = 'absolute inset-0 w-full h-full object-cover transition-opacity duration-1000 ease-in-out opacity-0';
        bgContainer.appendChild(newBg);

        // Wait for the new image to be added to the DOM, then fade it in
        setTimeout(() => {
          newBg.style.opacity = '1';
          // Fade out and remove the old background image
          const oldBg = bgContainer.querySelector('img');
          if (oldBg && oldBg !== newBg) {
            oldBg.style.opacity = '0';
            setTimeout(() => oldBg.remove(), 1000);
          }
        }, 50);

        // 2. Animate and update text content
        if (heroTextContainer) heroTextContainer.classList.remove('is-animating');
        if (heroImageContainer) heroImageContainer.classList.remove('is-animating');

        setTimeout(() => {
          if (heroTag) heroTag.textContent = slideData.tag;
          if (heroTitle) heroTitle.textContent = slideData.title;
          if (heroDesc) heroDesc.textContent = slideData.description;
          if (heroButton) heroButton.textContent = slideData.buttonText;
          if (heroPoster) {
            heroPoster.src = slideData.image;
            heroPoster.alt = slideData.title;
          }

          if (heroTextContainer) heroTextContainer.classList.add('is-animating');
          if (heroImageContainer) heroImageContainer.classList.add('is-animating');
        }, 100);

        // 3. Update indicators
        const indicators = indicatorsContainer.querySelectorAll('button');
        indicators.forEach((indicator, i) => {
          if (i === index) {
            indicator.classList.remove('bg-white/40', 'hover:bg-white/60');
            indicator.classList.add('bg-white', 'scale-110');
          } else {
            indicator.classList.remove('bg-white', 'scale-110');
            indicator.classList.add('bg-white/40', 'hover:bg-white/60');
          }
        });
      }

      function nextSlide() {
        const newIndex = (currentSlide + 1) % heroSlides.length;
        updateSlide(newIndex);
      }

      function prevSlide() {
        const newIndex = (currentSlide - 1 + heroSlides.length) % heroSlides.length;
        updateSlide(newIndex);
      }

      function startAutoSlide() {
        stopAutoSlide();
        autoSlideInterval = setInterval(nextSlide, autoSlideDelay);
      }

      function stopAutoSlide() {
        clearInterval(autoSlideInterval);
      }

      function resetAutoSlide() {
        stopAutoSlide();
        startAutoSlide();
      }

      // --- INITIALIZATION ---
      createIndicators();
      updateSlide(0);
      startAutoSlide();

      // --- EVENT LISTENERS ---
      if (prevButton) prevButton.addEventListener('click', () => { prevSlide(); resetAutoSlide(); });
      if (nextButton) nextButton.addEventListener('click', () => { nextSlide(); resetAutoSlide(); });

      // Pause on hover
      if (heroSection) {
        heroSection.addEventListener('mouseenter', stopAutoSlide);
        heroSection.addEventListener('mouseleave', startAutoSlide);
      }

    });

    // Mobile Menu
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
    const closeMobileMenuButton = document.getElementById('close-mobile-menu');

    function openMobileMenu() {
      // First show the overlay with fade-in effect
      mobileMenuOverlay.classList.remove('hidden');
      mobileMenuOverlay.style.opacity = '0';

      // Set initial state for smooth animation
      mobileMenu.style.opacity = '0';

      // Small delay for visual effect
      setTimeout(() => {
        // Animate overlay
        mobileMenuOverlay.style.opacity = '1';

        // Animate sidebar
        mobileMenu.classList.remove('-translate-x-full');
        mobileMenu.classList.add('translate-x-0', 'scale-100');
        mobileMenu.style.opacity = '1';

        document.body.classList.add('overflow-hidden'); // Prevent scrolling when menu is open
      }, 50);
    }

    function closeMobileMenu() {
      // First animate the sidebar with smooth collapse effect
      mobileMenu.style.transform = 'translateX(-100%) scale(0.95)';
      mobileMenu.style.opacity = '0.5';
      mobileMenuOverlay.style.opacity = '0';

      // Wait for animation to complete before hiding overlay
      setTimeout(() => {
        mobileMenu.classList.add('-translate-x-full');
        mobileMenu.classList.remove('translate-x-0', 'scale-100');
        mobileMenuOverlay.classList.add('hidden');
        document.body.classList.remove('overflow-hidden'); // Re-enable scrolling
        // Reset the opacity after animation completes
        mobileMenu.style.opacity = '1';
      }, 300);
    }

    mobileMenuButton.addEventListener('click', openMobileMenu);
    closeMobileMenuButton.addEventListener('click', closeMobileMenu);
    mobileMenuOverlay.addEventListener('click', closeMobileMenu);

    // Close mobile menu when clicking on a link
    const mobileMenuLinks = mobileMenu.querySelectorAll('a:not(.accordion-trigger)');
    mobileMenuLinks.forEach(link => {
      link.addEventListener('click', closeMobileMenu);
    });

    // Mobile Menu Accordion (separate from FAQ accordion)
    const mobileMenuAccordionTriggers = document.querySelectorAll('#mobile-menu .accordion-trigger');

    mobileMenuAccordionTriggers.forEach(trigger => {
      trigger.addEventListener('click', (e) => {
        e.preventDefault(); // Prevent default behavior
        const content = trigger.nextElementSibling;
        const icon = trigger.querySelector('svg');

        // Toggle active state
        content.classList.toggle('active');

        // Rotate the icon
        if (content.classList.contains('active')) {
          icon.style.transform = 'rotate(45deg)';
        } else {
          icon.style.transform = 'rotate(0)';
        }

        // Add enhanced animations for mobile menu accordions
        if (content.classList.contains('active')) {
          // Set max-height based on content's scrollHeight for smooth animation
          content.style.maxHeight = content.scrollHeight + 'px';
          content.style.opacity = '1';
          content.style.transform = 'translateY(0)';
          // Add a subtle highlight effect
          content.style.backgroundColor = 'rgba(240, 249, 255, 0.2)'; // Light blue background
          setTimeout(() => {
            content.style.backgroundColor = 'transparent';
          }, 500);
        } else {
          content.style.maxHeight = '0';
          content.style.opacity = '0';
          content.style.transform = 'translateY(-10px)';
        }
      });
    });

    // Tabs functionality
    const tabTriggers = document.querySelectorAll('.tab-trigger');
    const tabContents = document.querySelectorAll('.tab-content');

    if (tabTriggers.length > 0 && tabContents.length > 0) {
      // Function to activate a tab
      const activateTab = (trigger) => {
        const tabId = trigger.getAttribute('data-tab');

        // Remove active class from all triggers and contents
        tabTriggers.forEach(t => {
          // Reset all tabs to their default state
          t.classList.remove('bg-white', 'text-blue-600', 'shadow-lg');
          t.classList.add('text-white');

          // Update hover classes for inactive tabs
          t.classList.remove('hover:bg-blue-600');
          t.classList.add('hover:bg-blue-500', 'hover:text-white', 'hover:shadow-md');
        });

        tabContents.forEach(c => {
          c.classList.remove('active');
        });

        // Add active class to clicked trigger and corresponding content
        trigger.classList.add('bg-white', 'text-blue-600', 'shadow-lg');
        trigger.classList.remove('text-white');

        // Update hover classes for active tab
        trigger.classList.remove('hover:bg-blue-500', 'hover:shadow-md');
        trigger.classList.add('hover:bg-blue-600', 'hover:text-white');

        const tabContent = document.getElementById(tabId);
        if (tabContent) {
          // Add a small delay to ensure smooth transition
          setTimeout(() => {
            tabContent.classList.add('active');
          }, 50);
        } else {
          console.error('Tab content not found for ID:', tabId);
        }
      };

      // Add click event listeners to all tab triggers
      tabTriggers.forEach(trigger => {
        trigger.addEventListener('click', (e) => {
          e.preventDefault();
          activateTab(trigger);
        });
      });

      // Make sure the first tab is active by default
      if (tabTriggers[0]) {
        activateTab(tabTriggers[0]);
      }
    }

    // FAQ Accordion functionality (if not already initialized)
    if (typeof initFaqAccordions === 'undefined') {
      const faqAccordionTriggers = document.querySelectorAll('#faq .accordion-trigger');
      faqAccordionTriggers.forEach((trigger, index) => {
        trigger.addEventListener('click', (e) => {
          e.preventDefault();
          const content = trigger.nextElementSibling;
          const icon = trigger.querySelector('svg');
          const isActive = trigger.classList.contains('active');

          // Close all accordions
          faqAccordionTriggers.forEach(t => {
            t.classList.remove('active');
            const tContent = t.nextElementSibling;
            const tIcon = t.querySelector('svg');
            if (tContent) {
              tContent.classList.remove('active');
              tContent.style.display = 'none';
            }
            if (tIcon) tIcon.style.transform = 'rotate(0deg)';
          });

          // Open clicked accordion if it wasn't active
          if (!isActive) {
            trigger.classList.add('active');
            if (content) {
              content.classList.add('active');
              content.style.display = 'block';
            }
            if (icon) icon.style.transform = 'rotate(180deg)';
          }
        });
      });

      // Open first FAQ by default
      if (faqAccordionTriggers[0]) {
        faqAccordionTriggers[0].click();
      }
    }

    }); // Close DOMContentLoaded event listener
  </script>
  <!-- Removed duplicate tab script -->
  <!-- Removed duplicate accordion script that was conflicting with our FAQ accordion implementation -->
</body>

</html>