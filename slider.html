<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dynamic Hero Slider</title>
  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Google Fonts: Inter -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">

  <script>
    // Custom Tailwind theme configuration
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            sans: ['Inter', 'sans-serif'],
          },
          // Keyframe animations for slide transitions
          keyframes: {
            fadeIn: {
              '0%': { opacity: '0' },
              '100%': { opacity: '1' },
            },
            slideUp: {
              '0%': { transform: 'translateY(20px)', opacity: '0' },
              '100%': { transform: 'translateY(0)', opacity: '1' },
            },
            zoomIn: {
              '0%': { transform: 'scale(0.95)', opacity: '0' },
              '100%': { transform: 'scale(1)', opacity: '1' },
            }
          },
          // Animation utilities
          animation: {
            'fade-in': 'fadeIn 0.7s ease-in-out',
            'slide-up': 'slideUp 0.7s ease-out',
            'zoom-in': 'zoomIn 0.7s ease-out',
          },
        },
      },
    }
  </script>
  <style>
    /* Custom styles for a polished look */
    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Ensure animations can be re-triggered */
    .animate-on-change {
      animation: none;
    }

    .animate-on-change.is-animating {
      animation: slideUp 0.7s ease-out;
    }

    .animate-image-on-change {
      animation: none;
    }

    .animate-image-on-change.is-animating {
      animation: zoomIn 0.7s ease-out;
    }
  </style>
</head>

<body class="bg-gray-50 font-sans">

  <!-- Hero Slider Section -->
  <section id="hero-section" class="relative w-full overflow-hidden">
    <!-- Background Image Container -->
    <div id="hero-bg-container" class="absolute inset-0 w-full h-full">
      <!-- Initial Background Image (will be cloned for transitions) -->
      <img src="https://images.unsplash.com/photo-1521587760476-6c12a4b040da?q=80&w=2070&auto=format&fit=crop"
        alt="Background" class="w-full h-full object-cover transition-opacity duration-1000 ease-in-out">
    </div>

    <!-- Gradient Overlay for text readability -->
    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/50 to-black/20"></div>
    <div class="absolute inset-0 bg-gradient-to-r from-black/30 to-transparent"></div>

    <!-- Main Slider Content -->
    <div class="relative min-h-[90vh] sm:min-h-screen flex flex-col justify-center py-12 sm:py-0">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">

          <!-- Text Content: Added top margin on mobile for better spacing -->
          <div id="hero-text"
            class="text-white space-y-2 sm:space-y-4 text-center lg:text-left order-2 lg:order-1 mt-8 lg:mt-0">
            <div class="animate-on-change">
              <span
                class="inline-block bg-white/10 backdrop-blur-sm text-sm font-medium px-4 py-1.5 rounded-full mb-3">E-Books</span>
              <h1 class="text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight !leading-tight">
                E Books Bundle
              </h1>
              <p class="text-base sm:text-lg text-white/80 max-w-lg mx-auto lg:mx-0 mt-4">
                Discover a world of knowledge with our extensive E-Book collection. Save up to 75% on premium titles.
              </p>
              <button
                class="mt-6 sm:mt-8 bg-white text-gray-900 hover:bg-gray-200 px-8 py-3 rounded-full shadow-lg font-semibold transition-all duration-300 transform hover:scale-105">
                Explore Now
              </button>
            </div>
          </div>

          <!-- Image Content: Order changed for mobile-first layout -->
          <div class="flex justify-center lg:justify-end order-1 lg:order-2">
            <div id="hero-image-container"
              class="animate-image-on-change w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg">
              <div class="relative" style="padding-bottom: 125%;"> <!-- Aspect Ratio Box -->
                <img id="hero-poster"
                  src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                  alt="E Books Bundle"
                  class="absolute inset-0 w-full h-full object-cover rounded-2xl shadow-2xl border-4 border-white/20 transition-all duration-500 ease-in-out lg:transform lg:hover:rotate-3">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div class="absolute bottom-6 left-1/2 -translate-x-1/2 z-20 w-full px-4 flex items-center justify-center">
      <div class="flex items-center space-x-4 bg-black/20 backdrop-blur-md p-2 rounded-full">
        <button id="prev-slide" aria-label="Previous slide"
          class="p-2 text-white hover:bg-white/20 rounded-full transition-colors">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <div id="hero-indicators" class="flex items-center space-x-2">
          <!-- Indicators will be dynamically generated -->
        </div>

        <button id="next-slide" aria-label="Next slide"
          class="p-2 text-white hover:bg-white/20 rounded-full transition-colors">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  </section>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // --- DATA ---
      // Using the original slide titles and content with new, professional background images.
      const heroSlides = [
        {
          tag: "E-Books",
          title: "E Books Bundle",
          description: "Discover a world of knowledge with our extensive E-Book collection. Save up to 75% on premium titles.",
          buttonText: "Explore Now",
          image: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1521587760476-6c12a4b040da?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Shopping",
          title: "Online Shopping",
          description: "Shop from thousands of products with amazing discounts and cashback offers.",
          buttonText: "Shop Now",
          image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Payments",
          title: "Bill Payments",
          description: "Pay all your bills in one place and earn cashback on every transaction.",
          buttonText: "Pay Bills",
          image: "https://images.unsplash.com/photo-1556742502-ec7c0e9f34b1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1580577665996-613f3f3373c2?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Travel",
          title: "Travel Booking",
          description: "Book flights, hotels, buses, and more with exclusive discounts.",
          buttonText: "Book Now",
          image: "https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1476514525535-07fb3b4ae5f1?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Utilities",
          title: "Mobile Recharge",
          description: "Quick and easy recharge options for all your mobile and data needs.",
          buttonText: "Recharge",
          image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1614332287897-cdc485fa562d?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Security",
          title: "Insurance",
          description: "Secure your future with our comprehensive insurance solutions.",
          buttonText: "Get Insured",
          image: "https://images.unsplash.com/photo-1450101499163-c8848c66ca85?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1542838132-92c53300491e?q=80&w=1974&auto=format&fit=crop"
        },
        {
          tag: "Rewards",
          title: "Refer & Earn",
          description: "Invite your friends and family to earn rewards when they join our platform.",
          buttonText: "Refer Now",
          image: "https://images.unsplash.com/photo-1559526324-4b87b5e36e44?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1521791136064-7986c2920216?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Deals",
          title: "Cashback Offers",
          description: "Get up to 25% cashback on all your purchases across our platform.",
          buttonText: "View Offers",
          image: "https://images.unsplash.com/photo-1579621970795-87f54f59740f?q=80&w=2070&auto=format&fit=crop",
          backgroundImage: "https://images.unsplash.com/photo-1561414927-6d86591d0c4f?q=80&w=2070&auto=format&fit=crop"
        },
        {
          tag: "Support",
          title: "24/7 Support",
          description: "Our dedicated support team is available round the clock to assist you.",
          buttonText: "Contact Us",
          image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          backgroundImage: "https://images.unsplash.com/photo-1587560699334-cc426240169f?q=80&w=2070&auto=format&fit=crop"
        }
      ];

      // --- STATE ---
      let currentSlide = 0;
      let autoSlideInterval;
      const autoSlideDelay = 6000; // 6 seconds for a more relaxed feel

      // --- DOM ELEMENTS ---
      const heroSection = document.getElementById('hero-section');
      const bgContainer = document.getElementById('hero-bg-container');
      const prevButton = document.getElementById('prev-slide');
      const nextButton = document.getElementById('next-slide');
      const indicatorsContainer = document.getElementById('hero-indicators');

      const heroTextContainer = document.querySelector('#hero-text .animate-on-change');
      const heroTag = heroTextContainer.querySelector('span');
      const heroTitle = heroTextContainer.querySelector('h1');
      const heroDesc = heroTextContainer.querySelector('p');
      const heroButton = heroTextContainer.querySelector('button');
      const heroImageContainer = document.getElementById('hero-image-container');
      const heroPoster = document.getElementById('hero-poster');

      // --- FUNCTIONS ---

      /**
       * Generates indicator buttons for each slide.
       */
      function createIndicators() {
        indicatorsContainer.innerHTML = '';
        heroSlides.forEach((_, index) => {
          const button = document.createElement('button');
          button.classList.add('w-2', 'h-2', 'rounded-full', 'transition-all', 'duration-300');
          button.dataset.index = index;
          indicatorsContainer.appendChild(button);
          button.addEventListener('click', () => {
            updateSlide(index);
            resetAutoSlide();
          });
        });
      }

      /**
       * Main function to update the slide content and visuals.
       * @param {number} index - The index of the slide to display.
       */
      function updateSlide(index) {
        currentSlide = index;
        const slideData = heroSlides[index];

        // 1. Cross-fade background image
        const newBg = document.createElement('img');
        newBg.src = slideData.backgroundImage;
        newBg.alt = slideData.title + " background";
        newBg.className = 'absolute inset-0 w-full h-full object-cover transition-opacity duration-1000 ease-in-out opacity-0';
        bgContainer.appendChild(newBg);

        // Wait for the new image to be added to the DOM, then fade it in
        setTimeout(() => {
          newBg.style.opacity = '1';
          // Fade out and remove the old background image
          const oldBg = bgContainer.querySelector('img');
          if (oldBg && oldBg !== newBg) {
            oldBg.style.opacity = '0';
            setTimeout(() => oldBg.remove(), 1000); // Remove after transition
          }
        }, 50);

        // 2. Animate and update text content
        heroTextContainer.classList.remove('is-animating');
        heroImageContainer.classList.remove('is-animating');

        setTimeout(() => {
          heroTag.textContent = slideData.tag;
          heroTitle.textContent = slideData.title;
          heroDesc.textContent = slideData.description;
          heroButton.textContent = slideData.buttonText;
          heroPoster.src = slideData.image;
          heroPoster.alt = slideData.title;

          heroTextContainer.classList.add('is-animating');
          heroImageContainer.classList.add('is-animating');
        }, 100); // A small delay to allow class removal to register

        // 3. Update indicators
        const indicators = indicatorsContainer.querySelectorAll('button');
        indicators.forEach((indicator, i) => {
          if (i === index) {
            indicator.classList.remove('bg-white/50');
            indicator.classList.add('bg-white', 'w-4');
          } else {
            indicator.classList.remove('bg-white', 'w-4');
            indicator.classList.add('bg-white/50');
          }
        });
      }

      function nextSlide() {
        const newIndex = (currentSlide + 1) % heroSlides.length;
        updateSlide(newIndex);
      }

      function prevSlide() {
        const newIndex = (currentSlide - 1 + heroSlides.length) % heroSlides.length;
        updateSlide(newIndex);
      }

      function startAutoSlide() {
        stopAutoSlide();
        autoSlideInterval = setInterval(nextSlide, autoSlideDelay);
      }

      function stopAutoSlide() {
        clearInterval(autoSlideInterval);
      }

      function resetAutoSlide() {
        stopAutoSlide();
        startAutoSlide();
      }

      // --- INITIALIZATION ---
      createIndicators();
      updateSlide(0); // Set initial slide
      startAutoSlide();

      // --- EVENT LISTENERS ---
      prevButton.addEventListener('click', () => { prevSlide(); resetAutoSlide(); });
      nextButton.addEventListener('click', () => { nextSlide(); resetAutoSlide(); });

      // Pause on hover
      heroSection.addEventListener('mouseenter', stopAutoSlide);
      heroSection.addEventListener('mouseleave', startAutoSlide);
    });
  </script>
</body>

</html>